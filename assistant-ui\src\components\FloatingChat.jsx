import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faChevronUp, 
  faChevronDown, 
  faMicrophone, 
  faPaperclip, 
  faPaperPlane 
} from '@fortawesome/free-solid-svg-icons';
import { Button } from './ui/button.jsx';
import { Input } from './ui/input.jsx';

const FloatingChat = ({ onSubmit, isLoading = false }) => {
  const [isInputVisible, setIsInputVisible] = useState(false);
  const [input, setInput] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (input.trim() && onSubmit) {
      onSubmit(input.trim());
      setInput('');
    }
  };

  const handleFiles = (files) => {
    if (files && files.length > 0) {
      console.log('Files selected:', files);
      // Handle file upload logic here
    }
  };

  return (
    <div className="fixed bottom-6 left-0 right-0 z-50 pointer-events-none">
      <div className="flex flex-col items-center pb-2 px-4">
        {/* Toggle Button */}
        <button
          onClick={() => setIsInputVisible(!isInputVisible)}
          className="mb-2 p-1.5 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 pointer-events-auto group"
        >
          <FontAwesomeIcon
            icon={isInputVisible ? faChevronDown : faChevronUp}
            className="h-3 w-3 text-slate-600 dark:text-slate-400 group-hover:text-slate-800 dark:group-hover:text-slate-200 transition-colors"
          />
        </button>

        {/* Floating Input Container */}
        <div
          className={`transition-all duration-500 ease-in-out transform ${
            isInputVisible ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'
          } pointer-events-auto`}
        >
          <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-2xl border border-slate-200 dark:border-slate-700 p-3 max-w-2xl mx-auto backdrop-blur-sm">
            <form className="flex items-center space-x-2" onSubmit={handleSubmit}>
              <div className="flex-1 relative">
                <Input
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Message The Collective..."
                  disabled={isLoading}
                  className="w-full pl-3 pr-10 py-2 bg-transparent border-0 focus:ring-0 focus:outline-none text-slate-800 dark:text-slate-200 placeholder-slate-500 dark:placeholder-slate-400 text-sm"
                />
                {input.trim() && (
                  <div className="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-slate-400">
                    {input.length}
                  </div>
                )}
              </div>
              <Button
                variant="ghost"
                size="icon"
                type="button"
                onClick={() => alert('Microphone clicked!')}
                disabled={isLoading}
                className="h-8 w-8 hover:bg-slate-100 dark:hover:bg-slate-700 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors rounded-full"
              >
                <FontAwesomeIcon icon={faMicrophone} className="h-4 w-4" />
              </Button>
              <input 
                type="file" 
                id="file-input-floating-global" 
                multiple 
                onChange={(e) => handleFiles(e.target.files)} 
                className="hidden" 
              />
              <Button
                variant="ghost"
                size="icon"
                type="button"
                onClick={() => document.getElementById('file-input-floating-global').click()}
                disabled={isLoading}
                className="h-8 w-8 hover:bg-slate-100 dark:hover:bg-slate-700 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors rounded-full"
              >
                <FontAwesomeIcon icon={faPaperclip} className="h-4 w-4" />
              </Button>
              <Button
                type="submit"
                size="icon"
                disabled={isLoading || !input.trim()}
                className="h-8 w-8 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all disabled:opacity-50 disabled:cursor-not-allowed rounded-full"
              >
                <FontAwesomeIcon icon={faPaperPlane} className="h-3 w-3" />
              </Button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FloatingChat;
