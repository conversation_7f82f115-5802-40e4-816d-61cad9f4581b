use serde::{Deserialize, Serialize};
use std::process::{Child, Command};
use std::sync::{Arc, Mutex};
use tokio::time::{sleep, Duration};
use reqwest::Client;
use tauri::Manager;

#[derive(Debug)]
pub enum OllamaError {
    Reqwest(reqwest::Error),
    Io(std::io::Error),
    <PERSON><PERSON>(serde_json::Error),
    Custom(String),
    RequestFailed(String),
    ResponseParseError(String),
}

impl From<reqwest::Error> for OllamaError {
    fn from(err: reqwest::Error) -> Self {
        OllamaError::Reqwest(err)
    }
}

impl From<std::io::Error> for OllamaError {
    fn from(err: std::io::Error) -> Self {
        OllamaError::Io(err)
    }
}

impl From<serde_json::Error> for OllamaError {
    fn from(err: serde_json::Error) -> Self {
        OllamaError::J<PERSON>(err)
    }
}

impl std::fmt::Display for OllamaError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            OllamaError::Reqwest(err) => write!(f, "Reqwest error: {}", err),
            OllamaError::Io(err) => write!(f, "IO error: {}", err),
            OllamaError::Json(err) => write!(f, "JSON error: {}", err),
            OllamaError::Custom(err) => write!(f, "Custom error: {}", err),
            OllamaError::RequestFailed(err) => write!(f, "Request failed: {}", err),
            OllamaError::ResponseParseError(err) => write!(f, "Response parse error: {}", err),
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OllamaStatus {
    pub status: String,
}

pub struct OllamaClient {
    client: Client,
    ollama_process: Arc<Mutex<Option<Child>>>, // Changed to Arc<Mutex<Option<Child>>>
    base_url: String,
}

impl OllamaClient {
    pub fn new(base_url: String) -> Self {
        OllamaClient {
            client: Client::new(),
            ollama_process: Arc::new(Mutex::new(None)),
            base_url,
        }
    }



    pub async fn start_ollama(&self, server_path: Option<&str>) -> Result<(), OllamaError> {
        {
            let mut process_guard = self.ollama_process.lock().unwrap();
            if process_guard.is_some() {
                return Err(OllamaError::Custom("Ollama process already running".to_string()));
            }

            println!("Attempting to start Ollama...");

            let ollama_executable = if let Some(server_path) = server_path {
                // Use portable Ollama installation
                let server_dir = std::path::Path::new(server_path);
                let ollama_dir = server_dir.join("ollama");

                // Determine executable name based on OS
                let exe_name = if cfg!(target_os = "windows") {
                    "ollama.exe"
                } else {
                    "ollama"
                };

                let ollama_exe = ollama_dir.join(exe_name);

                if !ollama_exe.exists() {
                    return Err(OllamaError::Custom(format!(
                        "Ollama executable not found at: {}. Please install Ollama to the configured server directory.",
                        ollama_exe.display()
                    )));
                }

                ollama_exe.to_string_lossy().to_string()
            } else {
                // Fallback to system PATH
                "ollama".to_string()
            };

            println!("Starting Ollama from: {}", ollama_executable);

            // Set up environment for portable operation
            let mut cmd = Command::new(&ollama_executable);
            cmd.arg("serve");

            // Redirect stdout and stderr for debugging
            cmd.stdout(std::process::Stdio::piped());
            cmd.stderr(std::process::Stdio::piped());

            // Set OLLAMA_MODELS to point to our portable models directory
            if let Some(server_path) = server_path {
                let models_path = std::path::Path::new(server_path)
                    .parent()
                    .unwrap_or_else(|| std::path::Path::new(server_path))
                    .join("Models");
                println!("Set OLLAMA_MODELS to: {}", models_path.display());
                cmd.env("OLLAMA_MODELS", models_path);
            }

            match cmd.spawn() {
                Ok(mut child) => {
                    println!("Ollama process spawned successfully with PID: {:?}", child.id());

                    // Read initial output for debugging
                    if let Some(stdout) = child.stdout.take() {
                        use std::io::{BufRead, BufReader};
                        let reader = BufReader::new(stdout);
                        for line in reader.lines().take(5) { // Read first 5 lines
                            if let Ok(line) = line {
                                println!("Ollama stdout: {}", line);
                            }
                        }
                    }

                    *process_guard = Some(child);
                    println!("Ollama process started successfully.");
                },
                Err(e) => {
                    println!("Failed to spawn Ollama process: {}", e);
                    return Err(OllamaError::Custom(format!(
                        "Failed to start Ollama from {}: {}. Check if the executable exists and is valid.",
                        ollama_executable, e
                    )));
                }
            }
        } // Drop the guard here

        // Wait for Ollama to be ready
        for _ in 0..30 { // Try for 30 seconds
            if self.is_ollama_running().await {
                println!("Ollama is running and ready.");
                return Ok(());
            }
            sleep(Duration::from_secs(1)).await;
        }
        Err(OllamaError::Custom("Ollama did not start in time".to_string()))
    }



    pub async fn stop_ollama(&self) -> Result<(), OllamaError> {
        let mut process_guard = self.ollama_process.lock().unwrap();
        if let Some(mut child) = process_guard.take() {
            child.kill()?;
            child.wait()?;
            println!("Ollama process stopped.");
        }
        Ok(())
    }



    pub async fn get_ollama_status_internal(&self) -> Result<OllamaStatus, OllamaError> {
        if self.is_ollama_running().await {
            Ok(OllamaStatus { status: "running".to_string() })
        } else {
            Ok(OllamaStatus { status: "stopped".to_string() })
        }
    }

    pub async fn is_ollama_running(&self) -> bool {
        let url = format!("{}/api/tags", self.base_url);
        match self.client.get(&url).send().await {
            Ok(response) => response.status().is_success(),
            Err(_) => false,
        }
    }



    pub async fn get_ollama_models_internal(&self) -> Result<Vec<String>, OllamaError> {
        let url = format!("{}/api/tags", self.base_url);
        let response = self.client.get(&url).send().await?.json::<serde_json::Value>().await?;
        let models: Vec<String> = response["models"].as_array()
            .unwrap_or(&vec![])
            .iter()
            .filter_map(|m| m["name"].as_str().map(|s| s.to_string()))
            .collect();
        Ok(models)
    }



    pub async fn pull_ollama_model_internal(&self, model_name: &str) -> Result<(), OllamaError> {
        let url = format!("{}/api/pull", self.base_url);
        let body = serde_json::json!({ "name": model_name });
        self.client.post(&url).json(&body).send().await?.error_for_status()?;
        Ok(())
    }

    // Chat completion method for your dashboard
    pub async fn generate_completion(&self, model_name: &str, prompt: &str) -> Result<String, OllamaError> {
        let url = format!("{}/api/generate", self.base_url);
        let body = serde_json::json!({
            "model": model_name,
            "prompt": prompt,
            "stream": false
        });

        let response = self.client.post(&url).json(&body).send().await?;
        let json: serde_json::Value = response.json().await?;

        if let Some(response_text) = json["response"].as_str() {
            Ok(response_text.to_string())
        } else {
            Err(OllamaError::Custom("No response from model".to_string()))
        }
    }

    // Chat method for conversation-style interactions
    pub async fn chat(&self, model_name: &str, messages: Vec<serde_json::Value>) -> Result<String, OllamaError> {
        let url = format!("{}/api/chat", self.base_url);
        let body = serde_json::json!({
            "model": model_name,
            "messages": messages,
            "stream": false
        });

        let response = self.client.post(&url).json(&body).send().await?;
        let json: serde_json::Value = response.json().await?;

        if let Some(message) = json["message"]["content"].as_str() {
            Ok(message.to_string())
        } else {
            Err(OllamaError::Custom("No response from chat".to_string()))
        }
    }
}

impl Drop for OllamaClient {
    fn drop(&mut self) {
        if let Ok(mut process_guard) = self.ollama_process.lock() {
            if let Some(mut child) = process_guard.take() {
                let _ = child.kill();
                let _ = child.wait();
            }
        }
    }
}

// Tauri commands
#[tauri::command]
pub async fn start_ollama_server(app_handle: tauri::AppHandle) -> Result<(), String> {
    use crate::settings_manager::get_servers_path;

    // Get the configured server path
    let server_path = get_servers_path().await.unwrap_or_default();

    let client = app_handle.state::<OllamaClient>();

    // Pass the server path to start_ollama (None if empty)
    let server_path_option = if server_path.is_empty() {
        None
    } else {
        Some(server_path.as_str())
    };

    match client.start_ollama(server_path_option).await {
        Ok(_) => {
            println!("Ollama server started successfully from: {}",
                server_path_option.unwrap_or("system PATH"));
            Ok(())
        },
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn stop_ollama_server(app_handle: tauri::AppHandle) -> Result<(), String> {
    let client = app_handle.state::<OllamaClient>();
    match client.stop_ollama().await {
        Ok(_) => Ok(()),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn get_ollama_status(app_handle: tauri::AppHandle) -> Result<OllamaStatus, String> {
    let client = app_handle.state::<OllamaClient>();
    match client.get_ollama_status_internal().await {
        Ok(status) => Ok(status),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn get_ollama_models(app_handle: tauri::AppHandle) -> Result<Vec<String>, String> {
    let client = app_handle.state::<OllamaClient>();
    match client.get_ollama_models_internal().await {
        Ok(models) => Ok(models),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn pull_ollama_model(app_handle: tauri::AppHandle, model_name: String) -> Result<(), String> {
    let client = app_handle.state::<OllamaClient>();
    match client.pull_ollama_model_internal(&model_name).await {
        Ok(_) => Ok(()),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn send_chat_message(
    app_handle: tauri::AppHandle,
    model_name: String,
    prompt: String
) -> Result<String, String> {
    let client = app_handle.state::<OllamaClient>();
    match client.generate_completion(&model_name, &prompt).await {
        Ok(response) => Ok(response),
        Err(e) => Err(e.to_string()),
    }
}

// Helper function to check if Ollama is installed in the portable location
#[tauri::command]
pub async fn check_ollama_installation(_app_handle: tauri::AppHandle) -> Result<String, String> {
    use crate::settings_manager::get_servers_path;

    let server_path = get_servers_path().await.unwrap_or_default();

    if server_path.is_empty() {
        return Err("Server path not configured. Please set the Ollama server path in settings.".to_string());
    }

    let server_dir = std::path::Path::new(&server_path);
    let ollama_dir = server_dir.join("ollama");

    // Determine executable name based on OS
    let exe_name = if cfg!(target_os = "windows") {
        "ollama.exe"
    } else {
        "ollama"
    };

    let ollama_exe = ollama_dir.join(exe_name);

    if ollama_exe.exists() {
        Ok(format!("Ollama found at: {}", ollama_exe.display()))
    } else {
        Err(format!(
            "Ollama not found at: {}\n\nTo set up portable Ollama:\n1. Download Ollama from https://ollama.ai/download\n2. Extract/install to: {}",
            ollama_exe.display(),
            ollama_dir.display()
        ))
    }
}