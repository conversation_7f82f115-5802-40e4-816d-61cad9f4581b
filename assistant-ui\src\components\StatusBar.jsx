import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBell, faCog } from '@fortawesome/free-solid-svg-icons';
import { Button } from './ui/button.jsx';

const StatusBar = () => {
  const [systemMetrics, setSystemMetrics] = useState(null);
  const [metricsLoading, setMetricsLoading] = useState(true);
  const [metricsError, setMetricsError] = useState(false);

  // Fetch system metrics
  const fetchSystemMetrics = async () => {
    try {
      setMetricsLoading(true);
      setMetricsError(false);
      const metrics = await invoke('get_system_metrics');
      setSystemMetrics(metrics);
    } catch (error) {
      console.error('Error fetching system metrics:', error);
      setMetricsError(true);
      setSystemMetrics(null);
    } finally {
      setMetricsLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemMetrics();
    const interval = setInterval(fetchSystemMetrics, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="fixed bottom-0 left-0 right-0 h-6 bg-blue-600 dark:bg-blue-700 text-white text-xs flex items-center justify-between px-3 z-40">
      {/* Left Section - Status */}
      <div className="flex items-center space-x-3">
        <div className="flex items-center space-x-1">
          <div className={`w-2 h-2 rounded-full ${
            metricsLoading ? 'bg-yellow-300 animate-pulse' :
            metricsError ? 'bg-red-300' :
            systemMetrics ? 'bg-green-300' : 'bg-red-300'
          }`}></div>
          <span className="font-medium">
            {metricsLoading ? 'Loading...' :
             metricsError ? 'Error' :
             systemMetrics ? 'Online' : 'Offline'}
          </span>
        </div>
      </div>

      {/* Right Section - System Info & Controls */}
      <div className="flex items-center space-x-3">
        {/* System Metrics */}
        {systemMetrics && !metricsError && (
          <>
            <span className="hover:bg-blue-500 dark:hover:bg-blue-600 px-2 py-1 rounded cursor-pointer transition-colors">
              CPU: {systemMetrics.cpu_usage?.toFixed(1) ?? 'N/A'}%
            </span>
            <span className="hover:bg-blue-500 dark:hover:bg-blue-600 px-2 py-1 rounded cursor-pointer transition-colors">
              RAM: {systemMetrics.memory_usage?.used_gb?.toFixed(1) ?? 'N/A'}GB
            </span>
          </>
        )}
        {metricsError && (
          <>
            <span className="text-red-200">CPU: N/A</span>
            <span className="text-red-200">RAM: N/A</span>
          </>
        )}
        
        {/* Settings and Notifications */}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => alert('Notifications clicked!')}
          className="h-5 w-5 p-0 hover:bg-blue-500 dark:hover:bg-blue-600 text-white hover:text-white transition-colors"
        >
          <FontAwesomeIcon icon={faBell} className="h-3 w-3" />
        </Button>
        <Link to="/settings">
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 p-0 hover:bg-blue-500 dark:hover:bg-blue-600 text-white hover:text-white transition-colors"
          >
            <FontAwesomeIcon icon={faCog} className="h-3 w-3" />
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default StatusBar;
