use serde::{Deserialize, Serialize};
use std::fs;
use std::collections::HashMap;
use std::env;

// User preferences file path - using existing config file
const USER_PREFS_PATH: &str = "./Storage/System/User/userpref.config";

// Default paths - can be overridden by user preferences
const DEFAULT_INDEXED_DIRECTORY: &str = "./Storage/";
const DEFAULT_SYSTEM_LOG_PATH: &str = "./Storage/System/logs/";
const DEFAULT_PLUGINS_PATH: &str = "./Storage/Addons/Plugins/";
const DEFAULT_MCPS_PATH: &str = "./Storage/Addons/MCP/";
const DEFAULT_APIS_PATH: &str = "./Storage/Addons/API/";
const DEFAULT_MODELS_PATH: &str = "./Storage/System/Models/";
const DEFAULT_SERVERS_PATH: &str = "./Storage/System/Servers/";

// Helper function to resolve relative paths from app directory
pub fn resolve_path(path: &str) -> Result<std::path::PathBuf, String> {
    if path.starts_with("./") {
        // Get the directory where the executable is located
        let exe_path = env::current_exe().map_err(|e| format!("Failed to get executable path: {}", e))?;
        let exe_dir = exe_path.parent().ok_or("Failed to get executable directory")?;

        // Go up to find the project root (look for Storage directory)
        let mut current_dir = exe_dir.to_path_buf();
        loop {
            let storage_path = current_dir.join("Storage");
            if storage_path.exists() {
                let resolved = current_dir.join(&path[2..]);
                return Ok(resolved);
            }

            if let Some(parent) = current_dir.parent() {
                current_dir = parent.to_path_buf();
            } else {
                // Fallback to current working directory
                let cwd = env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))?;
                return Ok(cwd.join(&path[2..]));
            }
        }
    } else {
        Ok(std::path::PathBuf::from(path))
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UserPreferences {
    // Theme settings (matching existing config.json)
    pub dark_mode: bool,
    pub layout: String,
    pub theme: String,
    pub plugin_states: HashMap<String, bool>,

    // Path settings (extending existing config)
    pub system_log_path: String,
    #[serde(default = "default_indexed_directory")]
    pub indexed_directory: String,
    #[serde(default = "default_plugins_path")]
    pub plugins_path: String,
    #[serde(default = "default_mcps_path")]
    pub mcps_path: String,
    #[serde(default = "default_apis_path")]
    pub apis_path: String,
    #[serde(default = "default_models_path")]
    pub models_path: String,
    #[serde(default = "default_servers_path")]
    pub servers_path: String,

    // Date/time settings
    #[serde(default = "default_date_format")]
    pub date_format: String,
    #[serde(default = "default_time_format")]
    pub time_format: String,
    #[serde(default = "default_timezone")]
    pub timezone: String,

    // Application behavior settings
    #[serde(default = "default_auto_save")]
    pub auto_save: bool,
    #[serde(default = "default_startup_tab")]
    pub startup_tab: String,
    #[serde(default = "default_window_state")]
    pub window_maximized: bool,
    #[serde(default = "default_notifications")]
    pub notifications_enabled: bool,

    // Browser settings
    #[serde(default = "default_browser_homepage")]
    pub browser_homepage: String,
    #[serde(default = "default_browser_zoom")]
    pub browser_zoom_level: i32,
    #[serde(default = "default_browser_js")]
    pub browser_enable_javascript: bool,
    #[serde(default = "default_browser_images")]
    pub browser_enable_images: bool,
    #[serde(default = "default_browser_cookies")]
    pub browser_enable_cookies: bool,
    #[serde(default = "default_browser_popups")]
    pub browser_block_popups: bool,

    // Ollama/AI settings
    #[serde(default = "default_ollama_server")]
    pub ollama_server_url: String,
    #[serde(default = "default_ollama_model")]
    pub default_ollama_model: String,
    #[serde(default = "default_ollama_auto_start")]
    pub ollama_auto_start: bool,
}

// Default value functions for serde
fn default_indexed_directory() -> String { DEFAULT_INDEXED_DIRECTORY.to_string() }
fn default_plugins_path() -> String { DEFAULT_PLUGINS_PATH.to_string() }
fn default_mcps_path() -> String { DEFAULT_MCPS_PATH.to_string() }
fn default_apis_path() -> String { DEFAULT_APIS_PATH.to_string() }
fn default_models_path() -> String { DEFAULT_MODELS_PATH.to_string() }
fn default_servers_path() -> String { DEFAULT_SERVERS_PATH.to_string() }
fn default_date_format() -> String { "YYYY-MM-DD".to_string() }
fn default_time_format() -> String { "24h".to_string() }
fn default_timezone() -> String { "UTC".to_string() }

// Application behavior defaults
fn default_auto_save() -> bool { true }
fn default_startup_tab() -> String { "chat".to_string() }
fn default_window_state() -> bool { false }
fn default_notifications() -> bool { true }

// Browser defaults
fn default_browser_homepage() -> String { "https://www.google.com".to_string() }
fn default_browser_zoom() -> i32 { 100 }
fn default_browser_js() -> bool { true }
fn default_browser_images() -> bool { true }
fn default_browser_cookies() -> bool { true }
fn default_browser_popups() -> bool { true }

// Ollama/AI defaults
fn default_ollama_server() -> String { "http://localhost:11434".to_string() }
fn default_ollama_model() -> String { "llama2".to_string() }
fn default_ollama_auto_start() -> bool { false }

impl Default for UserPreferences {
    fn default() -> Self {
        Self {
            // Theme and UI settings
            dark_mode: false,
            layout: "default".to_string(),
            theme: "light".to_string(),
            plugin_states: HashMap::new(),

            // Path settings
            system_log_path: DEFAULT_SYSTEM_LOG_PATH.to_string(),
            indexed_directory: DEFAULT_INDEXED_DIRECTORY.to_string(),
            plugins_path: DEFAULT_PLUGINS_PATH.to_string(),
            mcps_path: DEFAULT_MCPS_PATH.to_string(),
            apis_path: DEFAULT_APIS_PATH.to_string(),
            models_path: DEFAULT_MODELS_PATH.to_string(),
            servers_path: DEFAULT_SERVERS_PATH.to_string(),

            // Date/time settings
            date_format: "YYYY-MM-DD".to_string(),
            time_format: "24h".to_string(),
            timezone: "UTC".to_string(),

            // Application behavior settings
            auto_save: default_auto_save(),
            startup_tab: default_startup_tab(),
            window_maximized: default_window_state(),
            notifications_enabled: default_notifications(),

            // Browser settings
            browser_homepage: default_browser_homepage(),
            browser_zoom_level: default_browser_zoom(),
            browser_enable_javascript: default_browser_js(),
            browser_enable_images: default_browser_images(),
            browser_enable_cookies: default_browser_cookies(),
            browser_block_popups: default_browser_popups(),

            // Ollama/AI settings
            ollama_server_url: default_ollama_server(),
            default_ollama_model: default_ollama_model(),
            ollama_auto_start: default_ollama_auto_start(),
        }
    }
}

impl UserPreferences {
    pub fn load() -> Self {
        let prefs_path = match resolve_path(USER_PREFS_PATH) {
            Ok(path) => path,
            Err(e) => {
                eprintln!("Failed to resolve user preferences path: {}, using fallback", e);
                std::path::PathBuf::from(USER_PREFS_PATH)
            }
        };

        if !prefs_path.exists() {
            // Create default preferences file
            let default_prefs = UserPreferences::default();
            if let Err(e) = default_prefs.save() {
                eprintln!("Failed to create default user preferences: {}", e);
            }
            return default_prefs;
        }

        match fs::read_to_string(prefs_path) {
            Ok(contents) => {
                match serde_json::from_str::<UserPreferences>(&contents) {
                    Ok(prefs) => prefs,
                    Err(e) => {
                        eprintln!("Failed to parse user preferences: {}, using defaults", e);
                        UserPreferences::default()
                    }
                }
            }
            Err(e) => {
                eprintln!("Failed to read user preferences: {}, using defaults", e);
                UserPreferences::default()
            }
        }
    }

    pub fn save(&self) -> Result<(), String> {
        let prefs_path = resolve_path(USER_PREFS_PATH)?;

        // Ensure directory exists
        if let Some(parent) = prefs_path.parent() {
            fs::create_dir_all(parent)
                .map_err(|e| format!("Failed to create user preferences directory: {}", e))?;
        }

        let json = serde_json::to_string_pretty(self)
            .map_err(|e| format!("Failed to serialize user preferences: {}", e))?;

        fs::write(prefs_path, json)
            .map_err(|e| format!("Failed to write user preferences: {}", e))?;

        Ok(())
    }
}


// Path functions using user preferences
#[tauri::command]
pub async fn get_indexed_directory() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.indexed_directory)
}

#[tauri::command]
pub async fn get_system_log_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.system_log_path)
}

#[tauri::command]
pub async fn get_plugins_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.plugins_path)
}

#[tauri::command]
pub async fn get_mcps_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.mcps_path)
}

#[tauri::command]
pub async fn get_apis_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.apis_path)
}

#[tauri::command]
pub async fn get_models_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.models_path)
}

#[tauri::command]
pub async fn get_servers_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.servers_path)
}

// Set commands for path configuration - now saves to user preferences
#[tauri::command]
pub async fn set_indexed_directory(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.indexed_directory = path.clone();
    prefs.save()?;
    Ok(format!("Indexed directory path set to: {}", path))
}

#[tauri::command]
pub async fn set_system_log_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.system_log_path = path.clone();
    prefs.save()?;
    Ok(format!("System log path set to: {}", path))
}

#[tauri::command]
pub async fn set_plugins_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.plugins_path = path.clone();
    prefs.save()?;
    Ok(format!("Plugins path set to: {}", path))
}

#[tauri::command]
pub async fn set_mcps_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.mcps_path = path.clone();
    prefs.save()?;
    Ok(format!("MCPs path set to: {}", path))
}

#[tauri::command]
pub async fn set_apis_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.apis_path = path.clone();
    prefs.save()?;
    Ok(format!("APIs path set to: {}", path))
}

#[tauri::command]
pub async fn set_models_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.models_path = path.clone();
    prefs.save()?;
    Ok(format!("Models path set to: {}", path))
}

#[tauri::command]
pub async fn set_servers_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.servers_path = path.clone();
    prefs.save()?;
    Ok(format!("Servers path set to: {}", path))
}

#[tauri::command]
pub async fn set_ollama_model_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.models_path = path.clone(); // Use models_path for ollama models
    prefs.save()?;
    Ok(format!("Ollama model path set to: {}", path))
}

// Ollama server specific commands (aliases for servers_path)
#[tauri::command]
pub async fn get_ollama_server_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.servers_path)
}

#[tauri::command]
pub async fn set_ollama_server_path(path: String) -> Result<String, String> {
    Ok(format!("Ollama server path set to: {}", path))
}



// User preferences commands (for userpref.config)
#[tauri::command]
pub async fn load_user_settings() -> Result<UserPreferences, String> {
    Ok(UserPreferences::load())
}

#[tauri::command]
pub async fn save_user_settings(settings: UserPreferences) -> Result<String, String> {
    settings.save().map(|_| "User settings saved successfully.".to_string())
}


#[derive(Debug, Serialize, Deserialize)]
pub struct DirectoryInfo {
    pub path: String,
    pub exists: bool,
    pub files: Vec<String>,
    pub directories: Vec<String>,
    pub error: Option<String>,
}

#[tauri::command]
pub async fn get_directory_info(path: String) -> Result<DirectoryInfo, String> {


    // Resolve relative paths to absolute paths using the same logic as other functions
    let dir_path = resolve_path(&path)?;

    if !dir_path.exists() {
        return Ok(DirectoryInfo {
            path: path.clone(),
            exists: false,
            files: vec![],
            directories: vec![],
            error: Some(format!("Directory does not exist: {}", dir_path.display())),
        });
    }

    if !dir_path.is_dir() {
        return Ok(DirectoryInfo {
            path: path.clone(),
            exists: true,
            files: vec![],
            directories: vec![],
            error: Some("Path is not a directory".to_string()),
        });
    }

    let mut files = Vec::new();
    let mut directories = Vec::new();

    match fs::read_dir(&dir_path) {
        Ok(entries) => {
            for entry in entries {
                match entry {
                    Ok(entry) => {
                        let file_name = entry.file_name().to_string_lossy().to_string();
                        if entry.path().is_dir() {
                            directories.push(file_name);
                        } else {
                            files.push(file_name);
                        }
                    }
                    Err(e) => {
                        return Ok(DirectoryInfo {
                            path: path.clone(),
                            exists: true,
                            files,
                            directories,
                            error: Some(format!("Error reading entry: {}", e)),
                        });
                    }
                }
            }
        }
        Err(e) => {
            return Ok(DirectoryInfo {
                path: path.clone(),
                exists: true,
                files: vec![],
                directories: vec![],
                error: Some(format!("Error reading directory: {}", e)),
            });
        }
    }

    // Sort for consistent output
    files.sort();
    directories.sort();

    Ok(DirectoryInfo {
        path: path.clone(),
        exists: true,
        files,
        directories,
        error: None,
    })
}